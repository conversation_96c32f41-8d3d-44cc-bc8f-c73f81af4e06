const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 3000;

// CORS 설정
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 한국투자증권 API 기본 URL
const KIS_API_BASE = {
    vps: 'https://openapivts.koreainvestment.com:29443',
    prod: 'https://openapi.koreainvestment.com:9443'
};

// 메인 페이지 서빙
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 헬스 체크 엔드포인트
app.get('/api/health-check', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        server: 'Node.js',
        version: process.version,
        uptime: process.uptime()
    });
});

// 토큰 발급 프록시
app.post('/api/token', async (req, res) => {
    try {
        const { environment, appkey, appsecret } = req.body;

        console.log(`🔑 토큰 발급 요청: ${environment} 환경`);

        const response = await fetch(`${KIS_API_BASE[environment]}/oauth2/tokenP`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                grant_type: 'client_credentials',
                appkey: appkey,
                appsecret: appsecret
            })
        });

        const data = await response.json();

        if (data.access_token) {
            console.log('✅ 토큰 발급 성공');
        } else {
            console.log('❌ 토큰 발급 실패:', data.msg1 || data.error);
        }

        res.json(data);
    } catch (error) {
        console.error('❌ 토큰 발급 오류:', error.message);
        res.status(500).json({ error: error.message });
    }
});

// 미국 주식 현재가 조회 프록시
app.get('/api/us-stock/price', async (req, res) => {
    try {
        const { environment, symbol, accessToken, appkey, appsecret } = req.query;
        
        const url = `${KIS_API_BASE[environment]}/uapi/overseas-price/v1/quotations/price?AUTH=&EXCD=NAS&SYMB=${symbol}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'authorization': `Bearer ${accessToken}`,
                'appkey': appkey,
                'appsecret': appsecret,
                'tr_id': 'HHDFS00000300'
            }
        });

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('주식 가격 조회 오류:', error);
        res.status(500).json({ error: error.message });
    }
});

// 미국 주식 1분봉 데이터 조회 프록시
app.get('/api/us-stock/minute', async (req, res) => {
    try {
        const { environment, symbol, accessToken, appkey, appsecret } = req.query;
        
        const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const url = `${KIS_API_BASE[environment]}/uapi/overseas-price/v1/quotations/inquire-time-itemchartprice?AUTH=&EXCD=NAS&SYMB=${symbol}&NMIN=1&PINC=1&NEXT=&NREC=120&FILL=&KEYB=${today}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'authorization': `Bearer ${accessToken}`,
                'appkey': appkey,
                'appsecret': appsecret,
                'tr_id': 'HHDFS76950200'
            }
        });

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('1분봉 데이터 조회 오류:', error);
        res.status(500).json({ error: error.message });
    }
});

// 미국 주식 주문 프록시
app.post('/api/us-stock/order', async (req, res) => {
    try {
        const {
            environment,
            accessToken,
            appkey,
            appsecret,
            accountNumber,
            accountProduct,
            symbol,
            quantity,
            orderType
        } = req.body;

        const trId = environment === 'vps' ? 'VTTT1002U' : 'TTTT1002U';

        const response = await fetch(`${KIS_API_BASE[environment]}/uapi/overseas-stock/v1/trading/order`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'authorization': `Bearer ${accessToken}`,
                'appkey': appkey,
                'appsecret': appsecret,
                'tr_id': trId
            },
            body: JSON.stringify({
                'CANO': accountNumber,
                'ACNT_PRDT_CD': accountProduct,
                'OVRS_EXCG_CD': 'NASD',
                'PDNO': symbol,
                'ORD_QTY': quantity.toString(),
                'OVRS_ORD_UNPR': '0',
                'ORD_SVR_DVSN_CD': '0',
                'ORD_DVSN': orderType === 'buy' ? '00' : '01'
            })
        });

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('주문 오류:', error);
        res.status(500).json({ error: error.message });
    }
});

// 미국 주식 시장 스캔 프록시 (조건 충족 종목 검색)
app.get('/api/us-stock/market-scan', async (req, res) => {
    try {
        const { environment, accessToken, appkey, appsecret, minRiseRate, minVolume } = req.query;

        console.log(`🔍 시장 스캔 요청: 상승률 ${minRiseRate}%+, 거래액 ${minVolume}만불+`);

        // 한국투자증권 API의 해외주식 조건검색 사용
        const response = await fetch(`${KIS_API_BASE[environment]}/uapi/overseas-price/v1/quotations/inquire-search`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'authorization': `Bearer ${accessToken}`,
                'appkey': appkey,
                'appsecret': appsecret,
                'tr_id': 'HHDFS76410000'
            },
            params: new URLSearchParams({
                'AUTH': '',
                'EXCD': 'NAS',
                'CO_YN_PRICECUR': '',
                'CO_ST_PRICECUR': '',
                'CO_EN_PRICECUR': '',
                'CO_YN_RATE': 'Y',
                'CO_ST_RATE': minRiseRate,
                'CO_EN_RATE': '1000',
                'CO_YN_VOLUME': 'Y',
                'CO_ST_VOLUME': Math.floor(minVolume * 10000), // 만불을 달러로 변환
                'CO_EN_VOLUME': '999999999999'
            })
        });

        const data = await response.json();

        if (data.output && data.output.length > 0) {
            console.log(`✅ 시장 스캔 완료: ${data.output.length}개 종목 발견`);
        } else {
            console.log('⚠️ 조건 충족 종목 없음');
        }

        res.json(data);
    } catch (error) {
        console.error('❌ 시장 스캔 오류:', error.message);
        res.status(500).json({ error: error.message });
    }
});

// 서버 시작
app.listen(PORT, () => {
    console.log('============================================================');
    console.log('🚀 한국투자증권 미국주식 자동매매 시스템');
    console.log('   Korea Investment US Stock Auto Trading System');
    console.log('============================================================');
    console.log(`📡 Node.js 서버가 포트 ${PORT}에서 실행 중입니다.`);
    console.log(`🌐 브라우저에서 http://localhost:${PORT} 으로 접속하세요.`);
    console.log('');
    console.log('✅ 설치 완료 상태:');
    console.log('   - Node.js: v' + process.version);
    console.log('   - 의존성 패키지: 설치 완료');
    console.log('   - CORS 프록시: 활성화');
    console.log('');
    console.log('⚠️  중요 안내:');
    console.log('   - 실제 거래 전에 모의투자로 충분히 테스트하세요!');
    console.log('   - API 키는 안전하게 관리하세요!');
    console.log('   - 투자 손실 책임은 사용자에게 있습니다!');
    console.log('');
    console.log('⏹️  서버 종료: Ctrl+C');
    console.log('============================================================');

    // 3초 후 브라우저 자동 열기
    setTimeout(() => {
        const open = require('child_process').exec;
        open(`start http://localhost:${PORT}`, (error) => {
            if (!error) {
                console.log('🔗 브라우저가 자동으로 열렸습니다.');
            }
        });
    }, 3000);
});

module.exports = app;
